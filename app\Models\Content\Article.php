<?php

namespace App\Models\Content;

use App\Traits\Article\ArticleRelationsTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use MongoDB\Laravel\Eloquent\HybridRelations;
use MongoDB\Laravel\Eloquent\Model;

class Article extends Model
{
    use ArticleRelationsTrait, SoftDeletes;

    protected $connection = 'mongodb';

    protected $fillable = [
        'user_id',
        'article_id',
        'archive',
        'title',
        'slug',
        'page',
        'category_id',
        'description',
        'author_id',
        'meta_title',
        'meta_description',
        'meta_tags',
        'view_count',
        'schema',
        'status',
        'faqs',
        'datePublishedEdit',
        'datePublished',
        'meta_search',
        'canonical',
        'article_replace_id',
        'active',
        'type',
        'sidebar',
        'ads',
        'ads_toggle',
    ];

    protected $casts = [
        'ads' => 'array',
    ];
}