<?php

namespace App\Traits\Article;

use App\Models\UserInteraction\Comment;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Article Relations Trait
 *
 * This trait contains all relationship methods for the Article model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Article
 */
trait ArticleRelationsTrait
{

}
