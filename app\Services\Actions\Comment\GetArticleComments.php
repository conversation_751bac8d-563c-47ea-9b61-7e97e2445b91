<?php

namespace App\Services\Actions\Comment;

use App\Enums\Product\StatusEnum;
use App\Models\UserInteraction\Comment;
use Illuminate\Database\Eloquent\Collection;

/**
 * Action class to retrieve comments related to a product.
 */
class GetArticleComments
{

    public function handle(array $data): Collection
    {
        $article = $data['article'];
        die("dsada $article->_id");
        return Comment::where('commentable_id', $article->_id)
            ->whereNull('parent_id')
            ->where('status', StatusEnum::CONFIRMED)
            ->with(['replies', 'user'])
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
