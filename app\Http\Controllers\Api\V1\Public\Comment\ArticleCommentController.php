<?php

namespace App\Http\Controllers\Api\V1\Public\Comment;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Comment\CreateArticleCommentRequest;
use App\Http\Resources\UserInteraction\CommentResource;
use App\Models\Content\Article;
use App\Services\Actions\Comment\CreateArticleComment;
use App\Services\Actions\Comment\GetArticleComments;
use Symfony\Component\HttpFoundation\JsonResponse;


class ArticleCommentController extends BaseController
{
    /**
     * Store a newly created article comment.
     *
     * Handles the creation of an article comment by validating the request,
     * processing it through the provided action, and returning a JSON response
     * with the created comment resource.
     *
     * @param CreateArticleCommentRequest $request The validated HTTP request instance containing comment data.
     * @param CreateArticleComment $action The action class responsible for creating the comment.
     * 
     * @return JsonResponse JSON response containing the newly created comment resource and a success message.
     */
    public function store(CreateArticleCommentRequest $request, CreateArticleComment $action): JsonResponse
    {
        $comment = $action->handle([
            "user_agent" => $request->userAgent(),
            "ip" => $request->ip(),
            ...$request->validated()
        ]);

        return $this->sendResponse(
            new CommentResource($comment),
            __('messages.comments.created')
        );
    }


    public function index(Article $article, GetArticleComments $action)
    {
        die($article);

        $comments = $action->handle(['article' => $article]);

        return $this->sendResponse(
            CommentResource::collection($comments),
            __('messages.comment.found')
        );
    }
}
