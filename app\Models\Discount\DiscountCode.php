<?php

namespace App\Models\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Models\Product\PriceUnit;
use App\Models\Product\Product;
use App\Models\Shopping\Invoice;
use App\Models\User\User;
use App\Traits\Models\Helpers\ShamsiCraetedDate;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
class DiscountCode extends Model
{
    use ShamsiCraetedDate;
    protected $fillable = [
        'uuid',
        'code',
        'user_id',
        'type',
        'amount',
        'is_active',
        'starts_at',
        'expires_at',
        'product_id',
        'use_count',
        'use_count_limit',
        'price_unit_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'type' => DiscountTypeEnum::class,
        'use_count' => 'integer',
        'use_count_limit' => 'integer',
    ];

    // Automatically generate UUID on creating
    protected static function booted()
    {
        static::creating(function ($model) {
            if (!$model->uuid) {
                $model->uuid = Str::uuid()->toString();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function invoice(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function priceUnit(): BelongsTo
    {
        return $this->belongsTo(PriceUnit::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function isValid(?string $productId = null): bool
    {
        $userId = auth()->id();

        $currectUser = $this->user_id == null || $userId == $this->user_id;
        $notExpired = is_null($this->expires_at) || now('Asia/Tehran')->lessThan($this->expires_at);
        $hasStarted = is_null($this->starts_at) || now('Asia/Tehran')->greaterThanOrEqualTo($this->starts_at);
        $productMatches = is_null($this->product_id) || $this->product_id == $productId;
        $usageLimitNotReached = is_null($this->use_count_limit) || $this->use_count < $this->use_count_limit;
        print ('currectUser: ' . $currectUser . "\n");
        print ('notExpired: ' . $notExpired . "\n");
        print ('hasStarted: ' . $hasStarted . "\n");
        print ('productMatches: ' . $productMatches . "\n");
        print ('usageLimitNotReached: ' . $usageLimitNotReached . "\n");
        return $currectUser && $this->is_active && $notExpired && $hasStarted && $productMatches && $usageLimitNotReached;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('starts_at')
                    ->orWhere('starts_at', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }
    public function scopeWithUser($query)
    {
        return $query->with('user');
    }

    public function scopeNotExhausted($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('use_count_limit')
                ->orWhereRaw('use_count < use_count_limit');
        });
    }



    public function ShamsiExpiresAt(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->expires_at ? shamsiDate(date: $this->expires_at) : null,
        );
    }

    public function ShamsiStartsAt(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->starts_at ? shamsiDate(date: $this->starts_at) : null,
        );
    }

    /**
     * Increment the use count for this discount code
     */
    public function incrementUseCount(): void
    {
        $this->increment('use_count');
    }

    /**
     * Check if the discount code has reached its usage limit
     */
    public function hasReachedUsageLimit(): bool
    {
        return !is_null($this->use_count_limit) && $this->use_count >= $this->use_count_limit;
    }

    /**
     * Get remaining uses for this discount code
     */
    public function getRemainingUses(): ?int
    {
        if (is_null($this->use_count_limit)) {
            return null; // Unlimited uses
        }

        return max(0, $this->use_count_limit - $this->use_count);
    }
}
